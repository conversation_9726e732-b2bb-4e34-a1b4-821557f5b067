/**
 * API配置 - 智能本地Mock数据策略
 * 🎯 核心理念：开发阶段优先使用本地数据，确保功能完整可用
 */

export const CONFIG = {
  // ==================== 🌐 环境配置 ====================

  // 正式API地址 (后端部署后填入实际地址)
  BASE_URL: 'http://localhost:8080',

  // Mock API地址 - 已失效，暂时不使用
  MOCK_URL: 'https://m1.apifoxmock.com/m1/6375493-6071797-default',

  // 🎯 核心策略：优先使用本地Mock数据，确保开发体验
  USE_MOCK:true,  // 改为 true，但实际会被本地数据覆盖

  // 超时时间 (毫秒)
  TIMEOUT: 8000,

  // 接口版本
  API_VERSION: 'v1',

  // ==================== 📁 接口路径配置 ====================

  API_PATHS: {
    // 👤 用户相关接口
    REGISTER: '/users/registration',              // 用户注册
    LOGIN: '/users/login',                        // 用户登录
    DELETE_ACCOUNT: '/users/account/delete/{usertype}',      // 注销账户
    UPDATE_ADDRESS: '/users/address/update/{address},{usertype}',      // 修改地址
    USER_INFO: '/users/info',                     // 获取用户信息
    UPDATE_USER_INFO: '/users/info/update',      // 更新用户信息

    // 📸 图片上传相关
    UPLOAD_IMAGE: '/users/images/upload/{userType}{imageType}',         // 上传照片

    // 🐾 动物信息相关
    ANIMAL_INFO: '/users/common/animalinfo',      // 获取流浪动物信息
    ANIMAL_DETAIL: '/users/common/animalinfo/{id}',    // 动物详情
    ADOPT_INFO: '/users/common/adoptInfo',        // 申请领养动物
    ADOPT_STATUS: '/users/common/adoptstatus',    // 查询领养状态

    // 🏪 商店相关
    SHOPS_LIST: '/users/common/shops',            // 访问线上宠物商店
    SHOP_DETAIL: '/users/common/shops',           // 商店详情
    SHOP_EVALUATION: '/users/common/shop/evaluation', // 查看用户对宠物商店的评价
    SHOP_PET_LIST: '/users/common/shop/pets/',  // 浏览线上宠物商店宠物
    SHOP_RESERVE: '/users/common/shop/reserve',         // 预购宠物
    DELETE_SHOP_EVALUATION: '/users/common/shopevaluation/delete',  // 删除对宠物商店的评价
    GET_MY_SHOP_EVALUATIONS: '/users/common/selfEvaluation/shop', // 查看自己对宠物商店的评价
    UPLOAD_PET_INFO: '/users/shop/post/information/pets', // 上传宠物信息
    GET_PETS_FOR_SHOP:'/users/shop/view/pets', // 查看宠物信息
    UPDATE_PET: '/users/shop/pets/update', // 修改宠物信息
    UPDATE_ORDER:'/users/shop/order/update{id}{status}', // 修改订单信息
    VIEW_EVALUATIONS: '/users/shop/view/evaluations', // 查看用户评价
    VIEW_ORDERS:'/users/shop/view/order', // 查看订单信息

    // 🏥 医院相关
    HOSPITAL_SEARCH: '/users/common/search/hospital', // 查询宠物医院
    HOSPITAL_EVALUATION: '/users/common/evaluation/hospital', // 医院评价
    HOSPITAL_RESERVE: '/users/common/hospital/reserve',  // 预约宠物医疗
    DELETE_HOSPITAL_EVALUATION: '/users/common/hospitalevaluation/delete', // 删除对宠物医院的评价
    GET_SELF_HOSPITAL_EVALUATIONS: '/users/common/selfEvaluation/hospital', // 查看自己对宠物医院的评价
    HOSPITAL_RESERVES: '/users/hospital/view/reserve', // 查看医疗预约情况
    GET_HOSPITAL_EVALUATIONS: '/users/hospital/view/evaluation', // /users/hospital/view/evaluation

    // ❤ 救助站相关
    EVALUATE_RESCUE_STATION: '/users/common/evaluate/rescuestation', // 评价救助站
    GET_RESCUE_EVALUATIONS: '/users/common/rescuestation/evaluation', // 查看用户对救助站的评价
    DELETE_RESCUE_EVALUATION: '/users/common/rescuestation/delete', // 删除对救助站的评价
    GET_MY_RESCUE_EVALUATIONS: '/users/common/selfEvaluation/rescueStation', // 查看自己对救助站的评价
    SEARCH_RESCUE_STATIONS: '/users/common/search/station', // 查询救助站
    VIEW_EVALUATIONS_FOR_RESCUE: '/users/rescuestation/view/evaluation', // 查看用户评价
    UPLOAD_ANIMAL_INFO: '/users/rescuestation/upload/animalinfor', // 上传流浪动物信息
    UPDATE_ADOPT_STATUS: '/users/rescuestation/update{ID}{status}', // 修改领养匹配状态
    VIEW_ADOPT_STATUS: '/users/rescuestation/view/adoptstatus', // 查看领养匹配状态
    GET_ANIMALS: '/users/rescuestation/view/animal', // 获取流浪动物信息
    UPDATE_ANIMAL_INFO: '/users/rescuestation/animals/update', // 修改流浪动物信息

    // 📚 指南相关
    ANIMAL_GUIDE: '/users/common/animalguide/{variety}',    // 动物指南

    // 🔍 搜索相关
    SEARCH_PETS: '/pets/search',                  // 宠物搜索
    SEARCH_SHOPS: '/shops/search',               // 商店搜索
    SEARCH_HOSPITALS: '/hospitals/search',       // 医院搜索

    // 📦 订单/预约相关
    PET_ORDER_STATUS: '/users/common/check/petsorder', // 查看宠物预购状态
    MEDICAL_RESERVE_STATUS: '/users/common/hospital/check/reserve', // 查看宠物医疗预约状态
    DELETE_PET_ORDER: '/users/common/petsorder/delete', // 删除宠物预订状态
    DELETE_HOSPITAL_RESERVE: '/users/common/hospitalreserve/delete', // 删除宠物医疗预约状态
    UPDATE_RESERVE_STATUS_BASE: '/users/hospital/reserve/update', // 修改预约状态

    // 🐱 领养相关
    DELETE_ADOPT_STATUS: '/users/common/adoptstatus/delete' // 删除领养状态信息
  },

  // ==================== 📤 上传配置 ====================

  // 上传文件大小限制（单位：MB）
  UPLOAD_FILE_SIZE_LIMIT: 10,

  // 🖼️ 图片类型配置
  IMAGE_TYPES: {
    AVATAR: '头像',
    SHOP_PHOTO: '商店照片',
    PET_PHOTO: '宠物照片',
    HOSPITAL_PHOTO: '医院照片',
    RESCUE_PHOTO: '救助站照片',
    STRAY_PHOTO: '流浪动物照片',
    LICENSE: '营业执照'
  },

  // 📷 支持的图片格式
  UPLOAD_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'gif', 'webp'],

  // 🖼️ 图片URL前缀（根据接口文档需要拼接）
  IMAGE_URL_PREFIX: 'D:/images',

  // ==================== ⚠️ API请求错误码 ====================

  ERROR_CODES: {
    SUCCESS: 200,             // ✅ 成功
    UNAUTHORIZED: 401,        // 🚫 未授权
    FORBIDDEN: 403,           // 🚫 禁止访问
    NOT_FOUND: 404,           // 🔍 未找到
    SERVER_ERROR: 500,        // 💥 服务器错误
    PARAM_ERROR: 400,         // 📝 参数错误
    TOKEN_EXPIRED: 10001,     // ⏰ Token过期
    TOKEN_INVALID: 10002,     // 🔑 Token无效
    USER_NOT_FOUND: 10003,    // 👤 用户不存在
    PASSWORD_ERROR: 10004,    // 🔐 密码错误
    USER_EXIST: 10005,        // 👥 用户已存在
    FILE_TOO_LARGE: 10006,    // 📁 文件过大
    FILE_TYPE_ERROR: 10007,   // 📄 文件类型错误
    FILE_UPLOAD_ERROR: 10008, // 📤 文件上传错误
    OPERATION_FAILED: 10009,  // ⚡ 操作失败
    DATA_NOT_FOUND: 10010     // 📊 数据未找到
  },

  // ==================== 👥 用户类型配置 ====================

  USER_TYPES: {
    COMMON: '普通用户',
    HOSPITAL: '宠物医院',
    SHOP: '宠物商店',
    RESCUE: '救助站'
  },

  // ==================== 📄 分页配置 ====================

  PAGE_CONFIG: {
    DEFAULT_PAGE: 1,
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 50
  },

  // ==================== 💾 本地存储键名 ====================

  STORAGE_KEYS: {
    TOKEN: 'token',
    USER_INFO: 'userInfo',
    SAVED_ACCOUNT: 'savedAccount',
    SAVED_ADDRESS: 'savedAddress',
    SAVED_USER_TYPE: 'savedUserType',
    APP_CONFIG: 'appConfig',
    CACHE_DATA: 'cacheData'
  },

  // ==================== 📱 应用配置 ====================

  APP_CONFIG: {
    // 应用名称
    APP_NAME: '宠物之家',

    // 应用版本
    APP_VERSION: '1.0.0',

    // 分享配置
    SHARE_CONFIG: {
      title: '宠物之家 - 找到你的毛孩子',
      desc: '专业的宠物领养和交流平台',
      path: '/pages/home/<USER>'
    },

    // 主题配置
    THEME_CONFIG: {
      primaryColor: '#FF6F61',
      secondaryColor: '#4eaaa8',
      backgroundColor: '#f5f5f5'
    }
  },

  // ==================== 🔧 开发环境配置 ====================

  DEV_CONFIG: {
    ENABLE_LOG: true,
    SHOW_MOCK_TIP: true,  // 开启提示，便于调试
    ENABLE_NETWORK_MONITOR: true,
    AUTO_FALLBACK_TO_LOCAL: true,  // 自动降级到本地数据
    USE_LOCAL_MOCK: true,  // ⭐ 核心开关：直接使用本地Mock数据

    // 🔧 接口优先级配置
    INTERFACE_PRIORITY: {
      // 核心接口 - 这些接口必须能够正常工作
      CORE_APIS: [
        '/users/login',
        '/users/registration',
        '/users/address/update',
        '/users/images/upload',
        '/users/account/delete'
      ],

      // 业务接口 - 优先使用本地数据，确保页面正常展示
      BUSINESS_APIS: [
        '/users/common/animalinfo',
        '/users/common/shops',
        '/users/common/search/hospital',
        '/users/common/adoptInfo',
        '/users/common/adoptstatus'
      ],

      // 次要接口 - 可以完全使用本地数据
      SECONDARY_APIS: [
        '/users/common/shop/evaluation',
        '/users/common/evaluation/hospital',
        '/users/common/animalguide'
      ]
    },

    // 🚀 智能接口调用策略
    SMART_API_STRATEGY: {
      // 当网络不可用时，自动使用本地数据
      OFFLINE_FALLBACK: true,
      // 优先使用本地数据，减少网络依赖
      LOCAL_FIRST: true,
      // 显示友好的错误提示
      FRIENDLY_ERROR: true,
      // 自动重试次数
      RETRY_COUNT: 0,  // 暂时关闭重试，直接使用本地数据
      // 缓存策略
      CACHE_STRATEGY: 'memory'
    }
  },

  // ==================== 🛠️ 工具方法 ====================

  /**
   * 🖼️ 获取完整的图片URL
   * @param {string} relativePath - 相对路径
   * @returns {string} 完整URL
   */
  getFullImageUrl: function(relativePath) {
    if (!relativePath) return '';

    // 如果已经是完整路径，直接返回
    if (relativePath.startsWith('http://') ||
        relativePath.startsWith('https://')) {
      return relativePath;
    }

    // 如果是 D:/images/ 开头，说明是本地路径，需要补充上前缀
    if (relativePath.startsWith('D:/images/')) {
      // 替换 D:/images/ 为 http://localhost:8080/images/
      return `http://localhost:8080/images/${relativePath.slice(8)}`;
    }

    // 否则就是相对路径，拼接前缀
    return `${this.IMAGE_URL_PREFIX}${relativePath}`;
  },

  /**
   * 📤 构造上传图片的URL--已检查，严格按照接口文档
   * @param {string} userType - 用户类型
   * @param {string} imageType - 图片类型
   * @returns {string} 上传URL
   * 接口: POST /users/images/upload/{userType}{imageType}
   */
  buildUploadUrl: function(userType, imageType) {
    return `${this.BASE_URL}${this.API_PATHS.UPLOAD_IMAGE}`
    .replace('{userType}', encodeURIComponent(userType))
    .replace('{imageType}', encodeURIComponent(imageType));
  },

  /**
   * 🗑️ 构造删除账户的URL--已检查
   * @param {string} userType - 用户类型
   * @returns {string} 删除URL
   */
  buildDeleteAccountUrl: function(userType) {
    return `${this.BASE_URL}${this.API_PATHS.DELETE_ACCOUNT.replace('{usertype}', encodeURIComponent(userType))}`;
  },

  /**
   * 📍 构造修改地址的URL--已检查，严格按照接口文档
   * @param {string} address - 地址
   * @param {string} usertype - 用户类型
   * @returns {string} 修改地址URL
   * 接口: PUT /users/address/update/{address},{usertype}
   */
  buildUpdateAddressUrl: function(address, usertype) {
    return `${this.BASE_URL}${this.API_PATHS.UPDATE_ADDRESS}`
    .replace('{address}', encodeURIComponent(address))
    .replace('{usertype}', encodeURIComponent(usertype));
  },

  /**
   * 📚 构造动物百科的URL--已检查，严格按照接口文档
   * @param {string} variety - 动物品种
   * @returns {string} 动物百科URL
   * 接口: GET /users/common/animalguide/{variety}
   */
  buildAnimalGuideUrl: function(variety) {
    return `${this.BASE_URL}${this.API_PATHS.ANIMAL_GUIDE.replace('{variety}', encodeURIComponent(variety))}`;
  },

  /**
   * ✅ 验证图片格式
   * @param {string} fileName - 文件名
   * @returns {boolean} 是否支持
   */
  validateImageFormat: function(fileName) {
    if (!fileName) return false;

    const extension = fileName.toLowerCase().split('.').pop();
    return this.UPLOAD_IMAGE_FORMATS.includes(extension);
  },

  /**
   * 📏 验证文件大小
   * @param {number} fileSize - 文件大小（字节）
   * @returns {boolean} 是否符合要求
   */
  validateFileSize: function(fileSize) {
    const maxSize = this.UPLOAD_FILE_SIZE_LIMIT * 1024 * 1024; // 转换为字节
    return fileSize <= maxSize;
  },

  /**
   * 🔧 获取API完整URL
   * @param {string} apiPath - API路径
   * @param {boolean} useMock - 是否使用Mock
   * @returns {string} 完整URL
   */
  getApiUrl: function(apiPath, useMock = this.USE_MOCK) {
    const baseUrl = useMock ? this.MOCK_URL : this.BASE_URL;
    return baseUrl ? `${baseUrl}${apiPath}` : apiPath;
  }
};

// ==================== 🔧 工具函数：智能接口调用 ====================

/**
 * 🎯 智能接口调用策略 - 全面优化
 * @param {string} apiPath - API路径
 * @param {Object} options - 配置选项
 * @returns {Object} 调用配置
 */
export function smartApiCall(apiPath, options = {}) {
  const {
    forceRemote = false,  // 是否强制使用远程接口
    fallbackData = null,  // 降级数据
    showError = false     // 是否显示错误
  } = options;

  // 检查是否为核心接口
  const isCoreApi = CONFIG.DEV_CONFIG.INTERFACE_PRIORITY.CORE_APIS.includes(apiPath);
  const isBusinessApi = CONFIG.DEV_CONFIG.INTERFACE_PRIORITY.BUSINESS_APIS.includes(apiPath);

  return {
    // 🎯 核心策略：优先使用本地数据
    useMock: !forceRemote && CONFIG.DEV_CONFIG.USE_LOCAL_MOCK,
    useLocal: CONFIG.DEV_CONFIG.USE_LOCAL_MOCK,

    // 错误处理策略
    hideError: !isCoreApi || !showError,
    showLoading: isCoreApi,  // 只有核心接口显示loading

    // 降级数据
    fallbackData: fallbackData,

    // 网络策略
    timeout: isCoreApi ? CONFIG.TIMEOUT : 3000,  // 核心接口超时时间更长
    retry: isCoreApi ? 1 : 0,  // 核心接口允许重试

    // 调试信息
    apiType: isCoreApi ? 'core' : (isBusinessApi ? 'business' : 'secondary'),
    localFirst: CONFIG.DEV_CONFIG.SMART_API_STRATEGY.LOCAL_FIRST
  };
}

// ==================== 🎛️ 预设接口调用配置 ====================

export const API_PRESETS = {
  // 👤 用户相关 - 核心接口
  USER: {
    useMock: false,
    showLoading: true,
    hideError: false,
    timeout: CONFIG.TIMEOUT
  },

  // 📤 上传相关 - 核心接口
  UPLOAD: {
    useMock: false,
    showLoading: true,
    hideError: false,
    timeout: CONFIG.TIMEOUT * 2,  // 上传超时时间更长
    retryCount: 1
  },

  // 📊 业务数据 - 本地优先
  BUSINESS: {
    useMock: true,
    showLoading: false,
    hideError: true,
    timeout: 3000,
    useLocal: true
  },

  // 🎨 展示数据 - 完全本地
  DISPLAY: {
    useMock: true,
    showLoading: false,
    hideError: true,
    timeout: 1000,
    useLocal: true,
    localFirst: true
  },

  // 🔍 搜索相关 - 快速响应
  SEARCH: {
    useMock: true,
    showLoading: false,
    hideError: true,
    timeout: 2000,
    useLocal: true,
    debounce: 500  // 防抖延迟
  }
};

// ==================== 📊 Mock数据生成工具 ====================

/**
 * 🎲 生成Mock用户数据
 * @param {Object} options - 配置选项
 * @returns {Object} Mock用户数据
 */
export function generateMockUser(options = {}) {
  const { userType = '普通用户' } = options;

  return {
    username: `Mock用户_${Date.now()}`,
    account: `mock_${Date.now()}@example.com`,
    address: `Mock城市_${Math.floor(Math.random() * 100)}`,
    usertype: userType,
    avatar: `${CONFIG.IMAGE_URL_PREFIX}/mock/avatar_${Date.now()}.jpg`,
    posts: Math.floor(Math.random() * 50),
    followers: Math.floor(Math.random() * 100),
    following: Math.floor(Math.random() * 80),
    bio: '这是一个Mock用户的个人简介'
  };
}

/**
 * 🐾 生成Mock动物数据
 * @param {number} count - 生成数量
 * @returns {Array} Mock动物数据列表
 */
export function generateMockAnimals(count = 10) {
  const animals = [];
  const types = ['狗', '猫', '兔子', '鸟'];
  const statuses = ['待领养', '已领养', '治疗中'];

  for (let i = 0; i < count; i++) {
    animals.push({
      id: Date.now() + i,
      name: `小${types[Math.floor(Math.random() * types.length)]}_${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      age: Math.floor(Math.random() * 10) + 1,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      image: `${CONFIG.IMAGE_URL_PREFIX}/mock/pet_${Date.now()}_${i}.jpg`,
      description: `这是一只可爱的小动物，需要一个温暖的家。`,
      location: `Mock城市_${Math.floor(Math.random() * 20)}`,
      contact: `联系人_${i + 1}`
    });
  }

  return animals;
}

// ==================== 📤 导出配置 ====================

// 保持原有的导出方式，确保向后兼容
export const baseURL = CONFIG.BASE_URL;
export const mockURL = CONFIG.MOCK_URL;
export const useMockData = CONFIG.USE_MOCK;
export const timeout = CONFIG.TIMEOUT;
export const apiVersion = CONFIG.API_VERSION;
export const uploadFileSizeLimit = CONFIG.UPLOAD_FILE_SIZE_LIMIT;
export const errorCodes = CONFIG.ERROR_CODES;
export const userTypes = CONFIG.USER_TYPES;

// 默认导出
export default {
  baseURL,
  mockURL,
  useMockData,
  timeout,
  apiVersion,
  uploadFileSizeLimit,
  errorCodes,
  userTypes,
  CONFIG,
  smartApiCall,
  API_PRESETS,
  generateMockUser,
  generateMockAnimals
};