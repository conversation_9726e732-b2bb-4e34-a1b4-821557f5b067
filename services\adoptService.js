/**
 * 领养相关接口服务
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 申请领养动物（普通用户）
 * @param {Object} data - 领养申请数据
 * @returns {Promise} 申请结果
 */
function applyAdopt(data) {
  return request.post('/users/common/adoptInfo', data, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查询领养状态（普通用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 领养状态列表
 */
function getAdoptStatus(params = {}) {
  return request.get('/users/common/adoptstatus', params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 删除领养状态信息（普通用户）
 * @param {string} adoptId - 领养ID
 * @returns {Promise} 删除结果
 */
function deleteAdoptStatus(adoptId) {
  return request.delete(`${API_PATHS.DELETE_ADOPT_STATUS}/${adoptId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看领养匹配状态（救助站）
 * @param {Object} params - 查询参数
 * @returns {Promise} 领养匹配状态列表
 */
function getAdoptStatusForRescue(params = {}) {
  return request.get('/users/rescuestation/view/adoptstatus', params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 修改领养匹配状态（救助站）
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
function updateAdoptStatus(data) {
  return request.put('/users/rescuestation/update', data, { useMock: CONFIG.USE_MOCK });
}

export default {
  applyAdopt,
  getAdoptStatus,
  deleteAdoptStatus,
  getAdoptStatusForRescue,
  updateAdoptStatus
};