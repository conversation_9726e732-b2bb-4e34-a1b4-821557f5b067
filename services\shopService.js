/**
 * 商店相关接口服务 - 添加本地Mock数据版本
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 访问线上宠物商店--已检查，严格按照接口文档
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始 (必需)
 * @param {number} params.pageSize - 每页数量 (必需)
 * @param {string} params.address - 用户位置 (必需)
 * @returns {Promise} 商店列表
 * 接口: POST /users/common/shops
 * 参数: page, pageSize, address (Body), Authorization (Header)
 * 返回: { code, message, total, data: [{ID, name, address, contact, license, storePhoto}] }
 */
function getShops(params = {}) {
  console.log('🚀 访问线上宠物商店请求:', params);

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 参数验证 - 严格按照接口文档要求
      const errors = validateShopsParams(params);
      if (errors.length > 0) {
        reject(new Error(errors[0]));
        return;
      }

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 商店列表使用本地Mock数据');
        const mockData = getMockShopDataForAPI(params);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '查询成功',
          total: mockData.total,
          data: mockData.list
        });
        return;
      }

      // 构造请求体数据
      const requestData = {
        page: parseInt(params.page),
        pageSize: parseInt(params.pageSize),
        address: params.address
      };

      console.log('🏪 发送商店列表请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.SHOPS_LIST, requestData, requestOptions).then(result => {
        console.log('✅ 商店列表响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 查询商店列表成功');

          // 处理图片URL
          let processedData = result.data || [];
          if (Array.isArray(processedData)) {
            processedData = processedData.map(item => {
              if (item.storePhoto && !item.storePhoto.startsWith('http')) {
                // 如果不是完整URL，拼接D:/images/
                item.storePhoto = `D:/images/${item.storePhoto}`;
              }
              return item;
            });
          }

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '查询成功',
            total: result.total || processedData.length,
            data: processedData
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查询商店列表失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 商店列表接口调用失败:', error);
        // 返回模拟数据作为降级方案
        const mockData = getMockShopDataForAPI(params);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '接口失败，使用本地数据',
          total: mockData.total,
          data: mockData.list
        });
      });

    } catch (err) {
      console.error('❌ 查询商店列表参数准备失败:', err);
      reject(new Error('查询商店列表失败，请检查参数'));
    }
  });
}

/**
 * 获取商店列表（原有接口 - 保持兼容性）
 * @param {Object} params - 查询参数
 * @returns {Promise} 商店列表
 */
function getShopList(params = {}) {
  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 商店列表使用本地Mock数据（getShopList）');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockShopData(),
      message: '使用本地Mock数据'
    });
  }

  return request.get('/users/common/shops', params, { useMock: CONFIG.USE_MOCK })
    .catch(error => {
      console.error('🔥 商店列表接口调用失败:', error);
      // 返回模拟数据作为降级方案
      return {
        code: CONFIG.ERROR_CODES.SUCCESS,
        data: getMockShopData(),
        message: '接口失败，使用本地数据'
      };
    });
}

/**
 * 根据地址获取模拟商店数据
 * @param {string} address - 搜索地址
 * @returns {Array} 过滤后的模拟商店数据列表
 */
function getMockShopDataByAddress(address) {
  console.log('🔍 获取Mock数据，地址参数:', address);

  const allShops = [
    {
      ID: 1,
      name: '萌宠天地宠物店',
      address: '武汉市洪山区光谷步行街A座201',
      contact: '027-87654321',
      license: '鄂武工商备字第123456号',
      storePhoto: '/assets/images/default-pet.png',
      description: '专业的宠物用品店，提供各种宠物食品、玩具和日用品。'
    },
    {
      ID: 2,
      name: '爱宠生活馆',
      address: '武汉市江汉区江汉路步行街88号',
      contact: '027-88776655',
      license: '鄂武工商备字第234567号',
      storePhoto: '/assets/images/default-pet.png',
      description: '高端宠物食品专营店，进口品牌齐全。'
    },
    {
      ID: 3,
      name: '宠物乐园',
      address: '武汉市武昌区司门口民主路12号',
      contact: '027-86543210',
      license: '鄂武工商备字第345678号',
      storePhoto: '/assets/images/default-pet.png',
      description: '创意宠物玩具专营店，让您的宠物快乐成长。'
    },
    {
      ID: 4,
      name: '汪星人小屋',
      address: '武汉市硚口区解放大道188号',
      contact: '027-85432109',
      license: '鄂武工商备字第456789号',
      storePhoto: '/assets/images/default-pet.png',
      description: '专注狗狗用品的专业商店。'
    },
    {
      ID: 5,
      name: '喵星球',
      address: '武汉市青山区和平大道300号',
      contact: '027-84321098',
      license: '鄂武工商备字第567890号',
      storePhoto: '/assets/images/default-pet.png',
      description: '猫咪用品专营店，猫奴的天堂。'
    },
    {
      ID: 6,
      name: '北京宠物之家',
      address: '北京市朝阳区三里屯街道12号',
      contact: '010-12345678',
      license: '京朝工商备字第111111号',
      storePhoto: '/assets/images/default-pet.png',
      description: '北京地区知名宠物店，服务周到。'
    },
    {
      ID: 7,
      name: '上海萌萌宠物店',
      address: '上海市浦东新区陆家嘴金融区88号',
      contact: '021-87654321',
      license: '沪浦工商备字第222222号',
      storePhoto: '/assets/images/default-pet.png',
      description: '上海高端宠物服务商店。'
    },
    {
      ID: 8,
      name: '广州宠物乐园',
      address: '广州市天河区珠江新城A座',
      contact: '020-98765432',
      license: '粤穗工商备字第333333号',
      storePhoto: '/assets/images/default-pet.png',
      description: '广州最大的宠物用品连锁店。'
    }
  ];

  console.log('📋 所有商店数据:', allShops);

  // 修复过滤逻辑：特殊处理"全国"
  if (!address || !address.trim() || address === '全国') {
    console.log('📄 返回全部数据（全国或空地址）');
    return allShops;
  }

  // 根据地址关键词过滤
  const keyword = address.toLowerCase().trim();
  const filtered = allShops.filter(shop =>
    shop.name.toLowerCase().includes(keyword) ||
    shop.address.toLowerCase().includes(keyword)
  );

  console.log('🔍 过滤后的数据:', filtered, '关键词:', keyword);
  return filtered;
}

/**
 * 评价商店（普通用户）
 * @param {Object} data - 评价数据
 * @returns {Promise} 评价结果
 */
function evaluateShop(data) {
  return request.post('/users/common/evaluateshop', data, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看用户对宠物商店的评价（普通用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.id - 商店ID
 * @returns {Promise} 评价列表
 */
function getShopEvaluations(params = {}) {
  // 🎯 使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 商店评价使用本地Mock数据');
    const mockData = getMockEvaluationData();
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: mockData,
      total: mockData.length,
      message: '使用本地Mock数据'
    });
  }

  const { page, pageSize, id } = params;

  // 参数校验
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('page 参数必须是大于 0 的整数'));
  }
  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('pageSize 参数必须是 1 到 100 之间的整数'));
  }
  if (!Number.isInteger(id)) {
    return Promise.reject(new Error('id 参数（商店ID）是必需的'));
  }

  const token = wx.getStorageSync('token');
  const headers = {};
  if (token) {
    headers.Authorization = token;
  }

  // 正式请求
  return request.post('/users/common/shop/evaluation', { page, pageSize, id }, {
    headers,
    useMock: CONFIG.USE_MOCK
  }).then(res => {
    if (res.code !== 200) {
      throw new Error(res.message || '接口返回错误');
    }

    return {
      code: res.code,
      message: res.message,
      total: res.total || 0,
      data: res.data || []
    };
  }).catch(error => {
    console.error('🔥 商店评价接口调用失败:', error);
    const mockData = getMockEvaluationData();
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: mockData,
      total: mockData.length,
      message: '接口失败，使用本地数据'
    };
  });
}

/**
 * 删除对宠物商店的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteShopEvaluation(evaluationId) {
  return request.delete(`${API_PATHS.DELETE_SHOP_EVALUATION}/${evaluationId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 浏览线上宠物商店宠物（普通用户）
 * @param {number} shopId - 商店ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议10-100
 * @returns {Promise} 宠物列表
 */
function getShopPets(shopId, params = {}) {
  console.log('🐾 获取商店宠物列表，参数:', { shopId, ...params });

  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 商店宠物使用本地Mock数据');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          data: getMockPetData(),
          message: '使用本地Mock数据'
        });
      }, 500);
    });
  }

  // 参数验证
  const { page = 1, pageSize = 10 } = params;

  // 验证必需参数
  if (!shopId) {
    console.error('❌ 商店ID不能为空');
    return Promise.reject(new Error('商店ID不能为空'));
  }

  // 验证参数类型和范围
  if (!Number.isInteger(page) || page < 1) {
    console.error('❌ 页码参数无效:', page);
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    console.error('❌ 每页数量参数无效:', pageSize);
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  if (!Number.isInteger(shopId) || shopId < 1) {
    console.error('❌ 商店ID参数无效:', shopId);
    return Promise.reject(new Error('商店ID必须是大于0的整数'));
  }

  // 获取认证令牌
  const token = wx.getStorageSync('token');

  // 构造请求体数据 - 严格按照接口文档格式
  const requestData = {
    page: page,
    pageSize: pageSize,
    id: shopId  // 注意：接口文档中参数名是 id，不是 shopId
  };

  console.log('📤 发送商店宠物请求（POST）:', requestData);

  // 构造请求选项
  const options = {
    useMock: CONFIG.USE_MOCK
  };

  // 添加认证头部（如果有token）
  if (token) {
    options.headers = {
      'Authorization': token
    };
  }

  // 发送POST请求到正确的端点
  return request.post('CONFIG.API.SHOP_PET_LIST', requestData, options)
    .then(response => {
      console.log('📥 商店宠物接口响应:', response);

      // 检查响应格式 - 按照接口文档处理
      if (response && response.code === 200) {
        // 处理响应数据
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          data: Array.isArray(response.data) ? response.data : []
        };

        // 处理宠物数据 - 按照接口文档要求
        if (result.data.length > 0) {
          result.data = result.data.map(pet => {
            // 处理图片路径 - 按照文档要求
            let processedPhoto = pet.photo || '';

            if (processedPhoto) {
              if (processedPhoto.startsWith('http')) {
                // 完整URL（http开头），直接使用
                console.log('✅ 宠物图片使用完整URL:', processedPhoto);
              } else {
                // 相对路径，需拼接D:/images/
                // 按照文档：若为相对路径，需拼接D:/images/（如/api/images/pet.jpg拼接为D:/images/api/images/pet.jpg）
                if (processedPhoto.startsWith('/')) {
                  processedPhoto = `D:/images${processedPhoto}`;
                } else {
                  processedPhoto = `D:/images/${processedPhoto}`;
                }
                console.log('🔧 宠物图片路径已拼接:', processedPhoto);
              }
            }

            // 确保price为数字类型并保留两位小数
            let processedPrice = pet.price || 0;
            if (typeof processedPrice === 'string') {
              processedPrice = parseFloat(processedPrice) || 0;
            }
            processedPrice = Number(processedPrice.toFixed(2));

            // 确保age和stock为整数
            let processedAge = parseInt(pet.age) || 0;
            let processedStock = parseInt(pet.stock) || 0;

            // 标准化性别字段
            let processedGender = pet.gender || '未知';
            if (processedGender === '雄' || processedGender === 'male' || processedGender === 'M') {
              processedGender = '公';
            } else if (processedGender === '雌' || processedGender === 'female' || processedGender === 'F') {
              processedGender = '母';
            }

            // 返回标准化的宠物数据，保持与现有代码兼容
            return {
              id: pet.ID || pet.id || Math.random().toString(36).substr(2, 9), // 接口返回ID，兼容现有代码的id
              ID: pet.ID || pet.id, // 保留原始ID字段
              name: pet.name || `${pet.breed || '可爱'}宠物`, // 如果没有name，用breed生成
              breed: pet.breed || '未知品种',
              age: processedAge,
              gender: processedGender,
              price: processedPrice,
              image: processedPhoto, // 兼容现有代码的image字段
              photo: processedPhoto, // 保留原始photo字段
              stock: processedStock,
              description: pet.description || `可爱的${pet.breed || '宠物'}，年龄${processedAge}个月，性格温顺。`
            };
          });
        }

        console.log('✅ 商店宠物数据处理完成:', result);
        return result;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '服务器错误');
      } else {
        console.error('❌ 未知响应格式:', response);
        throw new Error(response.message || '获取宠物列表失败');
      }
    })
    .catch(error => {
      console.error('🔥 获取商店宠物失败:', error);

      // 网络错误的友好处理 - 返回Mock数据作为降级方案
      if (error.message && error.message.includes('网络')) {
        console.log('🌐 网络错误，返回Mock数据');
        return {
          code: 200,
          message: '网络连接失败，使用本地数据',
          data: getMockPetData()
        };
      }

      // 其他错误也返回Mock数据作为降级方案
      console.log('🔄 接口失败，返回Mock数据作为降级方案');
      return {
        code: 200,
        message: '接口失败，使用本地数据',
        data: getMockPetData()
      };
    });
}

/**
 * 预购宠物（普通用户）
 * @param {Object} data - 预约数据
 * @returns {Promise} 预约结果
 */
function reservePet(data) {
  // 🎯 模拟预约成功
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 宠物预约使用本地Mock数据');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          data: {},
          message: '预约成功'
        });
      }, 800);
    });
  }

  // 验证必需参数
  if (!data.storeID || !Number.isInteger(data.storeID) || data.storeID <= 0) {
    return Promise.reject(new Error('商店ID必须是大于0的整数'));
  }

  if (!data.petID || !Number.isInteger(data.petID) || data.petID <= 0) {
    return Promise.reject(new Error('宠物ID必须是大于0的整数'));
  }

  if (!data.paymentTime || !data.paymentTime.trim()) {
    return Promise.reject(new Error('预购时间不能为空'));
  }

  // 确保请求包含认证头部
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject(new Error('请先登录'));
  }

  // 构造请求数据 - 严格按照接口文档格式
  const requestData = {
    storeID: data.storeID,
    petID: data.petID,
    paymentTime: data.paymentTime
  };

  console.log('📤 发送预约请求:', requestData);

  // 发送请求
  return request.post(CONFIG.API.SHOP_RESERVE, requestData, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  })
    .then(response => {
      console.log('📥 预约响应:', response);

      if (response && response.code === 200) {
        return response;
      } else if (response && response.code === 500) {
        throw new Error(response.message || '服务器错误');
      } else {
        throw new Error(response.message || '预约失败');
      }
    })
    .catch(error => {
      console.error('🔥 预约宠物失败:', error);
      throw error;
    });
}

/**
 * 查看宠物预购状态（普通用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议5-100
 * @returns {Promise} 预购状态列表
 */
function getPetOrderStatus(params = {}) {
  console.log('🔍 获取宠物预购状态，参数:', params);

  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 预购状态使用本地Mock数据');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          data: getMockOrderData(),
          total: getMockOrderData().length,
          message: '使用本地Mock数据'
        });
      }, 500);
    });
  }

  // 参数验证
  const { page = 1, pageSize = 10 } = params;

  // 验证参数类型和范围
  if (!Number.isInteger(page) || page < 1) {
    console.error('❌ 页码参数无效:', page);
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 5 || pageSize > 100) {
    console.error('❌ 每页数量参数无效:', pageSize);
    return Promise.reject(new Error('每页数量必须是5-100之间的整数'));
  }

  // 获取认证令牌
  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('❌ 用户未登录');
    return Promise.reject(new Error('请先登录'));
  }

  // 构造请求体数据 - 严格按照接口文档格式
  const requestData = {
    page: page,
    pageSize: pageSize
  };

  console.log('📤 发送预购状态请求（POST）:', requestData);

  // 构造请求选项
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  };

  // 发送POST请求到正确的端点
  return request.post(CONFIG.API.PET_ORDER_STATUS, requestData, options)
    .then(response => {
      console.log('📥 预购状态接口响应:', response);

      // 检查响应格式 - 按照接口文档处理
      if (response && response.code === 200) {
        // 处理响应数据
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.total || 0,
          data: Array.isArray(response.data) ? response.data : []
        };

        // 处理预购订单数据 - 按照接口文档要求
        if (result.data.length > 0) {
          result.data = result.data.map(order => {
            // 处理图片路径 - 按照文档要求
            let processedPhoto = order.photo || '';

            if (processedPhoto) {
              if (processedPhoto.startsWith('http')) {
                // 完整URL（http开头），直接使用
                console.log('✅ 订单图片使用完整URL:', processedPhoto);
              } else {
                // 相对路径，需拼接D:/images/
                // 按照文档：若为相对路径，需拼接D:/images/（如/api/images/pet.jpg拼接为D:/images/api/images/pet.jpg）
                if (processedPhoto.startsWith('/')) {
                  processedPhoto = `D:/images${processedPhoto}`;
                } else {
                  processedPhoto = `D:/images/${processedPhoto}`;
                }
                console.log('🔧 订单图片路径已拼接:', processedPhoto);
              }
            }

            // 确保price为数字类型并保留两位小数
            let processedPrice = order.price || 0;
            if (typeof processedPrice === 'string') {
              processedPrice = parseFloat(processedPrice) || 0;
            }
            processedPrice = Number(processedPrice.toFixed(2));

            // 返回标准化的订单数据，映射到页面期望的字段结构
            return {
              // 主要字段映射
              id: order.ID || Math.random().toString(36).substr(2, 9), // 订单ID
              ID: order.ID, // 保留原始ID字段

              // 宠物信息映射
              petName: order.breed || '未知宠物', // 用品种作为宠物名称
              petType: order.breed || '未知品种', // 品种信息
              petImage: processedPhoto, // 处理后的图片路径

              // 订单信息
              price: processedPrice, // 处理后的价格
              status: order.reservationStatus || '未知状态', // 预购状态
              orderTime: new Date().toLocaleString(), // 当前时间作为订单时间（API未提供此字段）

              // 原始API字段保留
              photo: processedPhoto, // 原始photo字段
              reservationStatus: order.reservationStatus, // 原始状态字段
              breed: order.breed, // 品种
              address: order.address || '未知地址' // 商店地址
            };
          });
        }

        console.log('✅ 预购状态数据处理完成:', result);
        return result;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '服务器错误');
      } else {
        console.error('❌ 未知响应格式:', response);
        throw new Error(response.message || '获取预购状态失败');
      }
    })
    .catch(error => {
      console.error('🔥 获取预购状态失败:', error);

      // 网络错误的友好处理 - 返回Mock数据作为降级方案
      if (error.message && error.message.includes('网络')) {
        console.log('🌐 网络错误，返回Mock数据');
        return {
          code: 200,
          message: '网络连接失败，使用本地数据',
          total: getMockOrderData().length,
          data: getMockOrderData()
        };
      }

      // 认证错误处理
      if (error.message && error.message.includes('登录')) {
        throw error; // 重新抛出登录错误，让页面处理
      }

      // 其他错误也返回Mock数据作为降级方案
      console.log('🔄 接口失败，返回Mock数据作为降级方案');
      return {
        code: 200,
        message: '接口失败，使用本地数据',
        total: getMockOrderData().length,
        data: getMockOrderData()
      };
    });
}


/**
 * 删除宠物预订状态（普通用户）
 * @param {string|number} orderId - 订单ID
 * @returns {Promise} 删除结果
 */
function deletePetOrder(orderId) {
  // 参数验证 - 确保ID为正整数（根据API文档要求）
  if (!orderId || !Number.isInteger(Number(orderId)) || Number(orderId) <= 0) {
    console.error('❌ 订单ID参数无效:', orderId);
    return Promise.reject(new Error('订单ID必须是大于0的整数'));
  }

  // 获取认证令牌（根据API文档要求）
  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('❌ 用户未登录');
    return Promise.reject(new Error('请先登录'));
  }

  console.log('🗑️ 删除预购订单，ID:', orderId);

  // 构造请求选项 - 添加Authorization头部（根据API文档要求）
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  };

  // 发送DELETE请求到正确的端点
  return request.delete(`${API_PATHS.DELETE_PET_ORDER}/${orderId}`, {}, options)
    .then(response => {
      console.log('📥 删除订单响应:', response);

      // 检查响应格式 - 按照接口文档处理（200成功，500失败）
      if (response && response.code === 200) {
        console.log('✅ 订单删除成功');
        return response;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '服务器错误');
      } else {
        console.error('❌ 删除失败:', response);
        throw new Error(response.message || '删除订单失败');
      }
    })
    .catch(error => {
      console.error('🔥 删除订单失败:', error);

      // 处理特殊错误状态码（根据API文档要求）
      if (error.statusCode === 403 || error.code === 403) {
        error.message = error.message || '无权限删除此订单';
      } else if (error.statusCode === 404 || error.code === 404) {
        error.message = error.message || '订单不存在';
      }

      throw error;
    });
}

/**
 * 上传宠物信息（商店用户）
 * @param {Object} data - 宠物信息数据
 * @param {string} data.breed - 宠物品种，最大50字符
 * @param {string} data.gender - 性别（"男"或"女"）
 * @param {number} data.age - 年龄（单位：月）
 * @param {number} data.stock - 库存数量，≥0
 * @param {string} data.photo - 宠物图片URL或文件路径
 * @param {number} data.price - 价格，保留2位小数，>0
 * @returns {Promise} 上传结果
 */
function uploadPetInfo(data) {
  console.log('🐾 开始上传宠物信息:', data);

  // 🎯 模拟上传成功（开发阶段）
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 宠物信息上传使用本地Mock数据');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '上传成功',
          data: {
            id: Math.floor(Math.random() * 1000) + 1
          }
        });
      }, 1000);
    });
  }

  // 参数验证
  if (!data || typeof data !== 'object') {
    console.error('❌ 宠物信息数据无效');
    return Promise.reject(new Error('宠物信息数据不能为空'));
  }

  // 验证必需字段
  const requiredFields = ['breed', 'gender', 'age', 'stock', 'photo', 'price'];
  for (const field of requiredFields) {
    if (data[field] === null || data[field] === undefined || String(data[field]).trim() === '') {
      console.error(`❌ 缺少必需参数: ${field}`);
      return Promise.reject(new Error(`${field}不能为空`));
    }
  }

  // 验证字段格式
  if (typeof data.breed !== 'string' || data.breed.length > 50) {
    console.error('❌ 品种参数无效:', data.breed);
    return Promise.reject(new Error('品种必须是不超过50个字符的字符串'));
  }

  if (!['男', '女'].includes(data.gender)) {
    console.error('❌ 性别参数无效:', data.gender);
    return Promise.reject(new Error('性别必须是"男"或"女"'));
  }

  if (!Number.isInteger(data.age) || data.age < 0) {
    console.error('❌ 年龄参数无效:', data.age);
    return Promise.reject(new Error('年龄必须是非负整数'));
  }

  if (!Number.isInteger(data.stock) || data.stock < 0) {
    console.error('❌ 库存参数无效:', data.stock);
    return Promise.reject(new Error('库存必须是非负整数'));
  }

  if (typeof data.photo !== 'string' || !data.photo.trim()) {
    console.error('❌ 图片参数无效:', data.photo);
    return Promise.reject(new Error('图片不能为空'));
  }

  if (typeof data.price !== 'number' || data.price <= 0) {
    console.error('❌ 价格参数无效:', data.price);
    return Promise.reject(new Error('价格必须是大于0的数字'));
  }

  // 获取认证令牌
  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('❌ 用户未登录');
    return Promise.reject(new Error('请先登录'));
  }

  // 构造请求数据 - 严格按照接口文档格式
  const requestData = {
    breed: String(data.breed).trim(),
    gender: data.gender,
    age: parseInt(data.age),
    stock: parseInt(data.stock),
    photo: data.photo,
    price: Number(data.price.toFixed(2))
  };

  console.log('📤 发送宠物信息上传请求:', requestData);

  // 构造请求选项
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  };

  // 如果是文件上传，需要特殊处理
  if (data.photo && data.photo.startsWith('http://tmp/') || data.photo.includes('tmp_')) {
    // 临时文件，需要使用文件上传
    console.log('📁 检测到临时文件，使用文件上传模式');
    options.isFileUpload = true;
  }

  // 发送POST请求到Mock地址
  return request.post(API_PATH.UPLOAD_PET_INFO, requestData, options)
    .then(response => {
      console.log('📥 宠物信息上传响应:', response);

      // 检查响应格式 - 按照接口文档处理
      if (response && response.code === 200) {
        console.log('✅ 宠物信息上传成功');
        return response;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '上传失败');
      } else {
        console.error('❌ 未知响应格式:', response);
        throw new Error(response.message || '上传宠物信息失败');
      }
    })
    .catch(error => {
      console.error('🔥 上传宠物信息失败:', error);

      // 处理特定错误状态码
      if (error.statusCode === 400 || error.code === 400) {
        error.message = error.message || '参数无效，请检查输入信息';
      } else if (error.statusCode === 403 || error.code === 403) {
        error.message = error.message || '无权限操作，请确认登录状态';
      } else if (error.statusCode === 500 || error.code === 500) {
        error.message = error.message || '服务器错误，请稍后重试';
      }

      throw error;
    });
}

/**
 * 查看宠物信息（商店用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.pageSize - 每页数量（建议10-100）
 * @returns {Promise} 宠物信息列表
 */
function getPetsForShop(params = {}) {
  const { page = 1, pageSize = 10 } = params;

  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须在1到100之间'));
  }

  const token = wx.getStorageSync('token');

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: token ? { Authorization: token } : {}
  };

  const requestBody = { page, pageSize };

  return request.post(API_PATH.GET_PETS_FOR_SHOP, requestBody, options)
    .then(res => {
      if (res.code === 200) {
        return {
          code: res.code,
          message: res.message,
          total: res.total || 0,
          data: Array.isArray(res.data) ? res.data.map(pet => ({
            id: pet.ID,
            breed: pet.breed,
            age: pet.age,
            gender: pet.gender,
            stock: pet.stock,
            photo: pet.photo,
            price: pet.price
          })) : []
        };
      } else {
        throw new Error(res.message || '获取宠物信息失败');
      }
    })
    .catch(err => {
      console.error('获取宠物信息失败:', err);
      throw err;
    });
}


/**
 * 修改宠物信息（商店用户）
 * @param {Object} data - 待更新的宠物信息
 * @param {number} data.ID - 宠物唯一标识（必填）
 * @param {string} [data.bread] - 品种（可选）
 * @param {string} [data.gender] - 性别："男" 或 "女"（可选）
 * @param {string} [data.photo] - 图片 URL（可选）
 * @param {number} [data.age] - 年龄（可选）
 * @param {number} [data.price] - 价格（可选）
 * @param {number} [data.stock] - 库存（可选）
 * @returns {Promise} 修改结果
 */
function updatePet(data) {
  console.log('🛠️ 准备修改宠物信息:', data);

  if (!data || typeof data !== 'object' || !data.ID) {
    console.error('❌ 参数错误，缺少 ID');
    return Promise.reject(new Error('ID 是必填字段'));
  }

  const token = wx.getStorageSync('token');
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  if (token) {
    options.headers.Authorization = token;
  }

  return request.put(API_PATH.UPDATE_PET, data, options)
    .then(response => {
      console.log('📥 修改宠物响应:', response);
      if (response && response.code === 200) {
        console.log('✅ 宠物信息修改成功');
        return response;
      } else {
        throw new Error(response?.message || '修改失败');
      }
    })
    .catch(error => {
      console.error('🔥 修改宠物信息失败:', error);
      throw error;
    });
}


/**
 * 查看订单信息（商店用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 订单列表
 */
function getOrders(params = { page: 1, pageSize: 10 }) {
  return request.post(API_PATH.VIEW_ORDERS, params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 修改订单信息（商店用户）
 * @param {Object} data - 订单信息
 * @param {number} data.id - 订单ID
 * @param {string} data.status - 新状态
 * @returns {Promise} 修改结果
 */
function updateOrder(data) {
  if (!data || typeof data !== 'object' || !data.id || !data.status) {
    console.error('❌ 参数错误，缺少 id 或 status');
    return Promise.reject(new Error('id 和 status 是必填字段'));
  }

  const token = wx.getStorageSync('token');
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  if (token) {
    options.headers.Authorization = token;
  }

  const url = API_PATH.UPDATE_ORDER(data.id, data.status);

  return request.put(url, {}, options)
    .then(response => {
      console.log('📥 修改订单响应:', response);
      if (response && response.code === 200) {
        console.log('✅ 订单修改成功');
        return response;
      } else {
        throw new Error(response?.message || '修改失败');
      }
    })
    .catch(error => {
      console.error('🔥 修改订单失败:', error);
      throw error;
    });
}

/**
 * 获取模拟订单数据
 * @returns {Array} 模拟订单数据列表
 */
function getMockOrderData() {
  return [
    {
      ID: 23,
      photo: 'https://loremflickr.com/400/400?lock=2580829926505763',
      price: 3500.00,
      reservationStatus: '待支付',
      breed: '布偶猫',
      address: '武汉市洪山区光谷步行街A座201',
      // 映射字段
      id: 'PET001',
      petName: '布偶猫',
      petType: '布偶猫',
      petImage: 'https://loremflickr.com/400/400?lock=2580829926505763',
      orderTime: '2024-03-15 14:30',
      status: '待支付'
    },
    {
      ID: 24,
      photo: '/images/pets/golden-retriever.jpg',
      price: 2800.50,
      reservationStatus: '已支付',
      breed: '金毛寻回犬',
      address: '武汉市江汉区江汉路步行街88号',
      // 映射字段
      id: 'PET002',
      petName: '金毛寻回犬',
      petType: '金毛寻回犬',
      petImage: 'D:/images/images/pets/golden-retriever.jpg',
      orderTime: '2024-03-14 10:20',
      status: '已支付'
    },
    {
      ID: 25,
      photo: '/api/images/teddy.png',
      price: 1800.00,
      reservationStatus: '已完成',
      breed: '泰迪犬',
      address: '武汉市武昌区司门口民主路12号',
      // 映射字段
      id: 'PET003',
      petName: '泰迪犬',
      petType: '泰迪犬',
      petImage: 'D:/images/api/images/teddy.png',
      orderTime: '2024-03-13 16:45',
      status: '已完成'
    }
  ];
}

/**
 * 查看用户评价（商店）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从0开始）
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 评价列表
 */
function getShopEvaluationsForShop(params = {}) {
  const token = wx.getStorageSync('token');

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {},
  };

  if (token) {
    options.headers.Authorization = token;
  }

  return request.post(API_PATH.VIEW_EVALUATIONS, params, options)
    .then(response => {
      console.log('📥 用户评价响应:', response);
      if (response && response.code === 200) {
        console.log('✅ 成功获取用户评价');
        return response;
      } else {
        throw new Error(response?.message || '获取评价失败');
      }
    })
    .catch(error => {
      console.error('🔥 获取用户评价失败:', error);
      throw error;
    });
}

/**
 * 获取模拟商店数据（原有方法保持兼容性）
 * @returns {Array} 模拟商店数据列表
 */
function getMockShopData() {
  return [
    {
      id: 1,
      name: '萌宠天地宠物店',
      type: '宠物用品',
      rating: 4.8,
      image: '/assets/images/default-pet.png',
      address: '北京市朝阳区三里屯街道',
      description: '专业的宠物用品店，提供各种宠物食品、玩具和日用品。店内环境整洁，服务热情。',
      businessHours: '09:00-21:00',
      contact: '010-12345678',
      distance: '1.2km'
    },
    {
      id: 2,
      name: '爱宠生活馆',
      type: '宠物食品',
      rating: 4.6,
      image: '/assets/images/default-pet.png',
      address: '上海市浦东新区陆家嘴金融区',
      description: '高端宠物食品专营店，进口品牌齐全，营养搭配专业，为您的爱宠提供最好的营养。',
      businessHours: '10:00-22:00',
      contact: '021-87654321',
      distance: '800m'
    },
    {
      id: 3,
      name: '宠物乐园',
      type: '宠物玩具',
      rating: 4.7,
      image: '/assets/images/default-pet.png',
      address: '广州市天河区珠江新城',
      description: '创意宠物玩具专营店，各种益智玩具应有尽有，让您的宠物快乐成长。',
      businessHours: '09:30-20:30',
      contact: '020-98765432',
      distance: '2.1km'
    },
    {
      id: 4,
      name: '宠美坊宠物美容',
      type: '宠物美容',
      rating: 4.9,
      image: '/assets/images/default-pet.png',
      address: '深圳市南山区科技园',
      description: '专业宠物美容服务，资深美容师，环境舒适，让您的爱宠焕然一新。',
      businessHours: '08:00-19:00',
      contact: '0755-56789012',
      distance: '1.8km'
    },
    {
      id: 5,
      name: '宠物健康中心',
      type: '宠物用品',
      rating: 4.5,
      image: '/assets/images/default-pet.png',
      address: '成都市武侯区',
      description: '综合性宠物服务中心，集用品销售、健康咨询、护理服务于一体。',
      businessHours: '09:00-20:00',
      contact: '028-87654321',
      distance: '3.2km'
    },
    {
      id: 6,
      name: '小萌宠物店',
      type: '宠物食品',
      rating: 4.4,
      image: '/assets/images/default-pet.png',
      address: '杭州市西湖区',
      description: '社区宠物店，价格实惠，服务贴心，是您身边的宠物好帮手。',
      businessHours: '08:30-21:30',
      contact: '0571-12345678',
      distance: '500m'
    }
  ];
}

/**
 * 获取模拟宠物数据
 * @returns {Array} 模拟宠物数据列表
 */
function getMockPetData() {
  return [
    {
      ID: 1,
      breed: '布偶猫',
      photo: '/assets/images/default-pet.png', // 相对路径，会被拼接为 D:/images/assets/images/default-pet.png
      price: 3500.00,
      age: 36, // 3岁 = 36个月
      gender: '雌',
      stock: 2
    },
    {
      ID: 2,
      breed: '金毛寻回犬',
      photo: 'https://example.com/pets/golden-retriever.jpg', // 完整URL，直接使用
      price: 2800.00,
      age: 48, // 4岁 = 48个月
      gender: '雄',
      stock: 1
    },
    {
      ID: 3,
      breed: '泰迪犬',
      photo: '/images/pets/teddy.jpg',
      price: 1800.50,
      age: 24, // 2岁 = 24个月
      gender: '雌',
      stock: 3
    },
    {
      ID: 4,
      breed: '英国短毛猫',
      photo: '/api/images/british-shorthair.png',
      price: 2200.00,
      age: 60, // 5岁 = 60个月
      gender: '雄',
      stock: 0 // 库存为0
    },
    {
      ID: 5,
      breed: '哈士奇',
      photo: 'https://loremflickr.com/400/400/husky',
      price: 2500.99,
      age: 36, // 3岁 = 36个月
      gender: '雄',
      stock: 2
    },
    {
      ID: 6,
      breed: '波斯猫',
      photo: '/pets/persian-cat.jpg',
      price: 4200.00,
      age: 18, // 1.5岁 = 18个月
      gender: '雌',
      stock: 1
    },
    {
      ID: 7,
      breed: '柯基犬',
      photo: 'https://picsum.photos/400/400?random=7',
      price: 3200.00,
      age: 30, // 2.5岁 = 30个月
      gender: '雄',
      stock: 4
    },
    {
      ID: 8,
      breed: '苏格兰折耳猫',
      photo: '/uploads/scottish-fold.png',
      price: 2600.75,
      age: 42, // 3.5岁 = 42个月
      gender: '雌',
      stock: 1
    },
    {
      ID: 9,
      breed: '比熊犬',
      photo: 'https://example.com/bichon-frise.jpg',
      price: 2100.00,
      age: 21, // 1.75岁 = 21个月
      gender: '雄',
      stock: 3
    },
    {
      ID: 10,
      breed: '美国短毛猫',
      photo: '/static/images/american-shorthair.jpg',
      price: 1900.50,
      age: 33, // 2.75岁 = 33个月
      gender: '雌',
      stock: 2
    }
  ];
}

/**
 * 获取模拟评价数据
 * @returns {Array} 模拟评价数据列表
 */
function getMockEvaluationData() {
  return [
    {
      id: 1,
      userName: '爱猫人士',
      userAvatar: '/assets/images/default-pet.png',
      rating: 5,
      content: '店里的宠物都很健康，服务也很好，强烈推荐！',
      createTime: '2024-03-15 14:30'
    },
    {
      id: 2,
      userName: '小王',
      userAvatar: '/assets/images/default-pet.png',
      rating: 4,
      content: '价格合理，品种丰富，店员很专业。',
      createTime: '2024-03-12 10:15'
    },
    {
      id: 3,
      userName: '宠物达人',
      userAvatar: '/assets/images/default-pet.png',
      rating: 5,
      content: '环境很好，宠物都很活泼健康，值得信赖的好店铺。',
      createTime: '2024-03-10 16:45'
    },
    {
      id: 4,
      userName: '新手铲屎官',
      userAvatar: '/assets/images/default-pet.png',
      rating: 4,
      content: '第一次买宠物，店员很耐心地介绍，很满意。',
      createTime: '2024-03-08 11:20'
    }
  ];
}

export default {
  // 新接口方法
  getShops,
  // 原有接口方法（保持兼容）
  getShopList,
  evaluateShop,
  getShopEvaluations,
  deleteShopEvaluation,
  getShopPets,
  reservePet,
  getPetOrderStatus,
  deletePetOrder,
  uploadPetInfo,
  getPetsForShop,
  updatePet,
  getOrders,
  updateOrder,
  getShopEvaluationsForShop
};

/**
 * 验证商店查询参数
 * @param {Object} params - 查询参数
 * @returns {Array} 错误信息数组
 */
function validateShopsParams(params) {
  const errors = [];

  // 必填字段检查
  if (!params.page || typeof params.page !== 'number') {
    errors.push('页码不能为空且必须为数字');
  }

  if (!params.pageSize || typeof params.pageSize !== 'number') {
    errors.push('每页数量不能为空且必须为数字');
  }

  if (!params.address || params.address.trim() === '') {
    errors.push('地址不能为空');
  }

  // 如果必填字段有问题，直接返回
  if (errors.length > 0) {
    return errors;
  }

  // 范围验证
  if (params.page < 1) {
    errors.push('页码必须大于等于1');
  }

  if (params.pageSize < 1 || params.pageSize > 100) {
    errors.push('每页数量必须在1-100之间');
  }

  return errors;
}

/**
 * 获取符合接口文档的模拟商店数据 - 分页版本
 * @param {Object} params - 查询参数
 * @returns {Object} 分页的模拟商店数据
 */
function getMockShopDataForAPI(params = {}) {
  const { page = 1, pageSize = 10, address = '' } = params;

  // 完整的Mock数据列表，严格按照接口文档格式
  const allShops = [
    {
      ID: 1,
      name: '萌宠天地宠物店',
      address: '武汉市洪山区光谷步行街A座201',
      contact: '027-87654321',
      license: '鄂武工商备字第123456号',
      storePhoto: '/assets/images/shop1.jpg'
    },
    {
      ID: 2,
      name: '爱宠生活馆',
      address: '武汉市江汉区江汉路步行街88号',
      contact: '027-88776655',
      license: '鄂武工商备字第234567号',
      storePhoto: '/assets/images/shop2.jpg'
    },
    {
      ID: 3,
      name: '宠物乐园',
      address: '武汉市武昌区司门口民主路12号',
      contact: '027-86543210',
      license: '鄂武工商备字第345678号',
      storePhoto: '/assets/images/shop3.jpg'
    },
    {
      ID: 4,
      name: '汪星人小屋',
      address: '武汉市硚口区解放大道188号',
      contact: '027-85432109',
      license: '鄂武工商备字第456789号',
      storePhoto: '/assets/images/shop4.jpg'
    },
    {
      ID: 5,
      name: '喵星球',
      address: '武汉市青山区和平大道300号',
      contact: '027-84321098',
      license: '鄂武工商备字第567890号',
      storePhoto: '/assets/images/shop5.jpg'
    },
    {
      ID: 6,
      name: '北京宠物之家',
      address: '北京市朝阳区三里屯街道12号',
      contact: '010-12345678',
      license: '京朝工商备字第111111号',
      storePhoto: '/assets/images/shop6.jpg'
    },
    {
      ID: 7,
      name: '上海萌萌宠物店',
      address: '上海市浦东新区陆家嘴金融区88号',
      contact: '021-87654321',
      license: '沪浦工商备字第222222号',
      storePhoto: '/assets/images/shop7.jpg'
    },
    {
      ID: 8,
      name: '广州宠物乐园',
      address: '广州市天河区珠江新城A座',
      contact: '020-98765432',
      license: '粤穗工商备字第333333号',
      storePhoto: '/assets/images/shop8.jpg'
    }
  ];

  // 根据地址过滤
  let filteredShops = allShops;
  if (address && address.trim() !== '' && address !== '全国') {
    const keyword = address.toLowerCase().trim();
    filteredShops = allShops.filter(shop =>
      shop.name.toLowerCase().includes(keyword) ||
      shop.address.toLowerCase().includes(keyword)
    );
  }

  // 分页处理
  const total = filteredShops.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const list = filteredShops.slice(startIndex, endIndex);

  return {
    total,
    list,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}